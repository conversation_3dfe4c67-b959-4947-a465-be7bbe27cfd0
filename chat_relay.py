from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # Allow cross-origin for development

# In-memory store for current conversation (demo purposes)
conversation_state = {
    "from_ai": [],     # AI to human messages
    "from_human": []   # Human to AI/customer messages
}

@app.route('/from_ai', methods=['POST'])
def receive_from_ai():
    data = request.json
    message = data.get("message")
    ticket_id = data.get("ticket_id")

    # Store message
    conversation_state["from_ai"].append({
        "ticket_id": ticket_id,
        "message": message
    })

    return jsonify({"status": "received by human team"}), 200

@app.route('/from_human', methods=['POST'])
def receive_from_human():
    data = request.json
    message = data.get("message")
    ticket_id = data.get("ticket_id")

    # Store message
    conversation_state["from_human"].append({
        "ticket_id": ticket_id,
        "message": message
    })

    return jsonify({"status": "message sent to customer"}), 200

@app.route('/get_for_human', methods=['GET'])
def get_messages_for_human():
    return jsonify(conversation_state["from_ai"])

@app.route('/get_for_customer', methods=['GET'])
def get_messages_for_customer():
    return jsonify(conversation_state["from_human"])

if __name__ == '__main__':
    app.run(debug=True, port=5005)
