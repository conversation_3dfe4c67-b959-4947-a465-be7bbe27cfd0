{"cells": [{"cell_type": "code", "execution_count": 1, "id": "42ee5d01", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "GOOGLE_API_KEY = \"AIzaSyBe8ug7iYqjbHkzotoS-WMihTxNqebwX9I\"  # add your GOOGLE API key here\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"]}, {"cell_type": "code", "execution_count": 6, "id": "da226577", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/3z/8tdq3ymd4h9g5v2nll8gtry40000gn/T/ipykernel_33930/2227604910.py:3: DeprecationWarning: Call to deprecated class Gemini. (Should use `llama-index-llms-google-genai` instead, using Google's latest unified SDK. See: https://docs.llamaindex.ai/en/stable/examples/llm/google_genai/)\n", "  llm = Gemini(\n"]}], "source": ["from llama_index.llms.gemini import Gemini\n", "\n", "llm = Gemini(\n", "    model=\"models/gemini-2.5-flash-preview-05-20\",\n", "    # api_key=\"some key\",  # uses GOOGLE_API_KEY env var by default\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "2ff767e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To my knowledge, **PHML** is not a widely recognized or standardized markup language like HTML, XML, Markdown, or even more specialized ones like SVG or MathML.\n", "\n", "It's possible that \"PHML\" could refer to:\n", "\n", "1.  **A very niche, proprietary, or internal markup language:** Some companies or specific projects might develop their own custom markup languages for particular needs, and they might use an acronym like PHML internally.\n", "2.  **A typo or misremembered acronym:**\n", "    *   Could it be related to **PHTML** (PHP Hypertext Markup Language), which is often used for PHP files that contain a mix of HTML and PHP code?\n", "    *   Could it be **PHP** itself, which is a scripting language often used to *generate* HTML?\n", "    *   Could it be **PML** (various meanings, including Project Markup Language or Policy Markup Language)?\n", "    *   Could it be a misspelling of another known language?\n", "3.  **An acronym for something completely unrelated to markup languages:** \"PHML\" could stand for something in a different domain (e.g., a scientific term, a company name, a medical abbreviation, etc.).\n", "4.  **A very new or emerging technology** that hasn't gained widespread adoption or documentation yet.\n", "\n", "**To help me understand what you're referring to, could you provide more context? For example:**\n", "\n", "*   Where did you encounter the term \"PHML\"? (e.g., in a specific software, a document, a website, a conversation, a job description?)\n", "*   What was the surrounding context or topic? (e.g., web development, data processing, a specific industry, a particular project?)\n", "*   Are there any associated file extensions (e.g., `.phml`)?\n", "*   Is it related to a specific company, product, or framework?\n", "\n", "With more information, I might be able to give you a more precise answer!\n"]}], "source": ["resp = llm.complete(\"What do you know about PHML\")\n", "print(resp)"]}, {"cell_type": "code", "execution_count": 9, "id": "f4021fb8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/3z/8tdq3ymd4h9g5v2nll8gtry40000gn/T/ipykernel_33930/2613127332.py:8: DeprecationWarning: Call to deprecated class GeminiEmbedding. (Should use `llama-index-embeddings-google-genai` instead, using Google's latest unified SDK. See: https://docs.llamaindex.ai/en/stable/examples/embeddings/google_genai/)\n", "  embed_model = GeminiEmbedding(\n"]}], "source": ["from llama_index.core import VectorStoreIndex, SimpleDirectoryReader\n", "\n", "from llama_index.core import Settings\n", "from llama_index.embeddings.gemini import GeminiEmbedding\n", "\n", "model_name = \"models/embedding-001\"\n", "\n", "embed_model = GeminiEmbedding(\n", "    model_name=model_name, api_key=GOOGLE_API_KEY,\n", ")\n", "Settings.llm = llm\n", "Settings.embed_model = embed_model\n", "\n", "documents = SimpleDirectoryReader(\"data\").load_data()\n", "index = VectorStoreIndex.from_documents(documents, llm=llm)"]}, {"cell_type": "code", "execution_count": 11, "id": "acbffcb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The provided information does not contain the PHML website.\n"]}], "source": ["query_engine = index.as_query_engine()\n", "response = query_engine.query(\"Can you find the PHML website?\")\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "phml_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}